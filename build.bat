@echo off
echo Building SincResampler...

REM Check if we have a C compiler available
where gcc >nul 2>&1
if %errorlevel% == 0 (
    echo Using GCC compiler
    set COMPILER=gcc
    set CFLAGS=-Wall -Wextra -O2 -std=c99 -I.
    set LDFLAGS=-lm
    goto :compile_gcc
)

where cl >nul 2>&1
if %errorlevel% == 0 (
    echo Using MSVC compiler
    set COMPILER=cl
    set CFLAGS=/W3 /O2 /I.
    set LDFLAGS=
    goto :compile_msvc
)

echo Error: No C compiler found. Please install GCC (MinGW) or Visual Studio.
echo You can install MinGW from: https://www.mingw-w64.org/
pause
exit /b 1

:compile_gcc
echo Compiling with GCC...
gcc %CFLAGS% -c sincResampler.c -o sincResampler.o
if %errorlevel% neq 0 goto :error

gcc %CFLAGS% -c wavhelper/wavhelper.c -o wavhelper.o
if %errorlevel% neq 0 goto :error

gcc %CFLAGS% -c testResampler.c -o testResampler.o
if %errorlevel% neq 0 goto :error

gcc %CFLAGS% -c example.c -o example.o
if %errorlevel% neq 0 goto :error

echo Linking testResampler...
gcc -o testResampler.exe sincResampler.o wavhelper.o testResampler.o %LDFLAGS%
if %errorlevel% neq 0 goto :error

echo Linking example...
gcc -o example.exe sincResampler.o example.o %LDFLAGS%
if %errorlevel% neq 0 goto :error

echo Creating static library...
ar rcs libsincresampler.a sincResampler.o wavhelper.o
if %errorlevel% neq 0 (
    echo Warning: Could not create static library (ar not found)
)

goto :success

:compile_msvc
echo Compiling with MSVC...
cl %CFLAGS% /c sincResampler.c
if %errorlevel% neq 0 goto :error

cl %CFLAGS% /c wavhelper/wavhelper.c
if %errorlevel% neq 0 goto :error

cl %CFLAGS% /c testResampler.c
if %errorlevel% neq 0 goto :error

cl %CFLAGS% /c example.c
if %errorlevel% neq 0 goto :error

echo Linking testResampler...
cl /Fe:testResampler.exe sincResampler.obj wavhelper.obj testResampler.obj %LDFLAGS%
if %errorlevel% neq 0 goto :error

echo Linking example...
cl /Fe:example.exe sincResampler.obj example.obj %LDFLAGS%
if %errorlevel% neq 0 goto :error

echo Creating static library...
lib /out:sincresampler.lib sincResampler.obj wavhelper.obj
if %errorlevel% neq 0 (
    echo Warning: Could not create static library
)

goto :success

:success
echo.
echo Build completed successfully!
echo.
echo Generated files:
if exist testResampler.exe echo   testResampler.exe - Test program
if exist example.exe echo   example.exe - Simple example
if exist libsincresampler.a echo   libsincresampler.a - Static library (GCC)
if exist sincresampler.lib echo   sincresampler.lib - Static library (MSVC)
echo.
echo To run tests: testResampler.exe
echo To run example: example.exe
echo.
pause
exit /b 0

:error
echo.
echo Build failed!
pause
exit /b 1
