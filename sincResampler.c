#include "sincResampler.h"
#include <math.h>
#include <stdlib.h>
#include <string.h>
#include <assert.h>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

// Helper function to calculate sinc scale factor
static double sincScaleFactor(double ioRatio) {
    double sincScaleFactor = ioRatio > 1.0 ? 1.0 / ioRatio : 1.0;
    sincScaleFactor *= 0.9; // Empirical adjustment to avoid aliasing
    return sincScaleFactor;
}

// Aligned memory allocation (simplified version)
static void* alignedMalloc(size_t size, size_t alignment) {
    // For simplicity, just use regular malloc
    // In production code, you might want proper aligned allocation
    (void)alignment; // Suppress unused parameter warning
    return malloc(size);
}

static void alignedFree(void* ptr) {
    if (ptr) free(ptr);
}

// Convolution function (C implementation)
static float convolveC(const float* inputPtr, const float* k1, const float* k2, 
                       double kernelInterpolationFactor) {
    float sum1 = 0.0f;
    float sum2 = 0.0f;
    
    for (size_t n = 0; n < KERNEL_SIZE; ++n) {
        sum1 += inputPtr[n] * k1[n];
        sum2 += inputPtr[n] * k2[n];
    }
    
    return (float)((1.0 - kernelInterpolationFactor) * sum1 + 
                   kernelInterpolationFactor * sum2);
}

// Update regions in the input buffer
static void updateRegions(SincResampler* resampler, int secondLoad) {
    resampler->r0 = resampler->inputBuffer + (secondLoad ? KERNEL_SIZE : KERNEL_SIZE / 2);
    resampler->r3 = resampler->r0 + resampler->requestFrames - KERNEL_SIZE;
    resampler->r4 = resampler->r0 + resampler->requestFrames - KERNEL_SIZE / 2;
    resampler->blockSize = resampler->r4 - resampler->r2;
    
    assert(resampler->r1 == resampler->inputBuffer);
    assert(resampler->r2 - resampler->r1 == resampler->r4 - resampler->r3);
    assert(resampler->r2 < resampler->r3);
}

// Initialize the kernel
static void initializeKernel(SincResampler* resampler) {
    // Blackman window parameters
    static const double kAlpha = 0.16;
    static const double kA0 = 0.5 * (1.0 - kAlpha);
    static const double kA1 = 0.5;
    static const double kA2 = 0.5 * kAlpha;
    
    const double sincScaleFactorValue = sincScaleFactor(resampler->ioSampleRateRatio);
    
    for (size_t offsetIdx = 0; offsetIdx <= KERNEL_OFFSET_COUNT; ++offsetIdx) {
        const float subsampleOffset = (float)offsetIdx / KERNEL_OFFSET_COUNT;
        
        for (size_t i = 0; i < KERNEL_SIZE; ++i) {
            const size_t idx = i + offsetIdx * KERNEL_SIZE;
            const float preSinc = (float)(M_PI * 
                ((int)i - (int)(KERNEL_SIZE / 2) - subsampleOffset));
            resampler->kernelPreSincStorage[idx] = preSinc;
            
            // Compute Blackman window
            const float x = (i - subsampleOffset) / KERNEL_SIZE;
            const float window = (float)(kA0 - kA1 * cos(2.0 * M_PI * x) + 
                                        kA2 * cos(4.0 * M_PI * x));
            resampler->kernelWindowStorage[idx] = window;
            
            // Compute windowed sinc
            resampler->kernelStorage[idx] = (float)(
                window * ((preSinc == 0.0f) ? sincScaleFactorValue : 
                         (sin(sincScaleFactorValue * preSinc) / preSinc)));
        }
    }
}

// SincResampler implementation
SincResampler* sincResamplerCreate(double ioSampleRateRatio, 
                                   size_t requestFrames,
                                   SincResamplerCallback readCallback,
                                   void* callbackUserData) {
    if (requestFrames <= KERNEL_SIZE) return NULL;
    
    SincResampler* resampler = (SincResampler*)calloc(1, sizeof(SincResampler));
    if (!resampler) return NULL;
    
    resampler->ioSampleRateRatio = ioSampleRateRatio;
    resampler->readCallback = readCallback;
    resampler->callbackUserData = callbackUserData;
    resampler->requestFrames = requestFrames;
    resampler->inputBufferSize = requestFrames + KERNEL_SIZE;
    
    // Allocate aligned memory buffers
    resampler->kernelStorage = (float*)alignedMalloc(sizeof(float) * KERNEL_STORAGE_SIZE, 32);
    resampler->kernelPreSincStorage = (float*)alignedMalloc(sizeof(float) * KERNEL_STORAGE_SIZE, 32);
    resampler->kernelWindowStorage = (float*)alignedMalloc(sizeof(float) * KERNEL_STORAGE_SIZE, 32);
    resampler->inputBuffer = (float*)alignedMalloc(sizeof(float) * resampler->inputBufferSize, 32);
    
    if (!resampler->kernelStorage || !resampler->kernelPreSincStorage || 
        !resampler->kernelWindowStorage || !resampler->inputBuffer) {
        sincResamplerDestroy(resampler);
        return NULL;
    }
    
    // Initialize region pointers
    resampler->r1 = resampler->inputBuffer;
    resampler->r2 = resampler->inputBuffer + KERNEL_SIZE / 2;
    
    // Initialize buffers
    memset(resampler->kernelStorage, 0, sizeof(float) * KERNEL_STORAGE_SIZE);
    memset(resampler->kernelPreSincStorage, 0, sizeof(float) * KERNEL_STORAGE_SIZE);
    memset(resampler->kernelWindowStorage, 0, sizeof(float) * KERNEL_STORAGE_SIZE);
    
    sincResamplerFlush(resampler);
    initializeKernel(resampler);
    
    return resampler;
}

void sincResamplerDestroy(SincResampler* resampler) {
    if (!resampler) return;
    
    alignedFree(resampler->kernelStorage);
    alignedFree(resampler->kernelPreSincStorage);
    alignedFree(resampler->kernelWindowStorage);
    alignedFree(resampler->inputBuffer);
    free(resampler);
}

void sincResamplerFlush(SincResampler* resampler) {
    if (!resampler) return;
    
    resampler->virtualSourceIdx = 0.0;
    resampler->bufferPrimed = 0;
    memset(resampler->inputBuffer, 0, sizeof(float) * resampler->inputBufferSize);
    updateRegions(resampler, 0);
}

size_t sincResamplerChunkSize(const SincResampler* resampler) {
    if (!resampler) return 0;
    return (size_t)(resampler->blockSize / resampler->ioSampleRateRatio);
}

void sincResamplerSetRatio(SincResampler* resampler, double ioSampleRateRatio) {
    if (!resampler || fabs(resampler->ioSampleRateRatio - ioSampleRateRatio) < 1e-15) {
        return;
    }
    
    resampler->ioSampleRateRatio = ioSampleRateRatio;
    
    const double sincScaleFactorValue = sincScaleFactor(ioSampleRateRatio);
    for (size_t offsetIdx = 0; offsetIdx <= KERNEL_OFFSET_COUNT; ++offsetIdx) {
        for (size_t i = 0; i < KERNEL_SIZE; ++i) {
            const size_t idx = i + offsetIdx * KERNEL_SIZE;
            const float window = resampler->kernelWindowStorage[idx];
            const float preSinc = resampler->kernelPreSincStorage[idx];
            
            resampler->kernelStorage[idx] = (float)(
                window * ((preSinc == 0.0f) ? sincScaleFactorValue : 
                         (sin(sincScaleFactorValue * preSinc) / preSinc)));
        }
    }
}

float sincResamplerAlgorithmicDelaySeconds(int sourceRateHz) {
    return (float)(1.0 / sourceRateHz * KERNEL_SIZE / 2);
}

void sincResamplerResample(SincResampler* resampler, size_t frames, float* destination) {
    if (!resampler || !destination) return;

    size_t remainingFrames = frames;

    // Prime the input buffer at the start
    if (!resampler->bufferPrimed && remainingFrames) {
        resampler->readCallback(resampler->callbackUserData, resampler->requestFrames, resampler->r0);
        resampler->bufferPrimed = 1;
    }

    // Resample
    const double currentIoRatio = resampler->ioSampleRateRatio;
    const float* const kernelPtr = resampler->kernelStorage;

    while (remainingFrames) {
        for (int i = (int)ceil((resampler->blockSize - resampler->virtualSourceIdx) / currentIoRatio);
             i > 0; --i) {
            assert(resampler->virtualSourceIdx < resampler->blockSize);

            // Calculate kernel offsets
            const int sourceIdx = (int)resampler->virtualSourceIdx;
            const double subsampleRemainder = resampler->virtualSourceIdx - sourceIdx;

            const double virtualOffsetIdx = subsampleRemainder * KERNEL_OFFSET_COUNT;
            const int offsetIdx = (int)virtualOffsetIdx;

            // Get kernel pointers
            const float* const k1 = kernelPtr + offsetIdx * KERNEL_SIZE;
            const float* const k2 = k1 + KERNEL_SIZE;

            // Input pointer
            const float* const inputPtr = resampler->r1 + sourceIdx;

            // Interpolation factor
            const double kernelInterpolationFactor = virtualOffsetIdx - offsetIdx;

            *destination++ = convolveC(inputPtr, k1, k2, kernelInterpolationFactor);

            // Advance virtual index
            resampler->virtualSourceIdx += currentIoRatio;

            if (!--remainingFrames) return;
        }

        // Wrap back around
        resampler->virtualSourceIdx -= resampler->blockSize;

        // Copy r3, r4 to r1, r2
        memcpy(resampler->r1, resampler->r3, sizeof(float) * KERNEL_SIZE);

        // Reinitialize regions if necessary
        if (resampler->r0 == resampler->r2) {
            updateRegions(resampler, 1);
        }

        // Refresh buffer
        resampler->readCallback(resampler->callbackUserData, resampler->requestFrames, resampler->r0);
    }
}

// Utility functions
void floatS16ToS16(const float* src, size_t length, int16_t* dest) {
    for (size_t i = 0; i < length; ++i) {
        float sample = src[i];
        if (sample > 1.0f) sample = 1.0f;
        if (sample < -1.0f) sample = -1.0f;
        dest[i] = (int16_t)(sample * 32767.0f);
    }
}

void s16ToFloatS16(const int16_t* src, size_t length, float* dest) {
    for (size_t i = 0; i < length; ++i) {
        dest[i] = (float)src[i] / 32768.0f;
    }
}

// PushSincResampler callback function
static void pushSincResamplerCallback(void* userData, size_t frames, float* destination) {
    PushSincResampler* pushResampler = (PushSincResampler*)userData;

    // Ensure we are only asked for available samples
    assert(pushResampler->sourceAvailable == frames);

    if (pushResampler->firstPass) {
        // Provide dummy input on first pass
        memset(destination, 0, frames * sizeof(float));
        pushResampler->firstPass = 0;
        return;
    }

    if (pushResampler->sourcePtr) {
        // Copy float source
        memcpy(destination, pushResampler->sourcePtr, frames * sizeof(float));
    } else if (pushResampler->sourcePtrInt) {
        // Convert int16 to float
        s16ToFloatS16(pushResampler->sourcePtrInt, frames, destination);
    }

    pushResampler->sourceAvailable -= frames;
}

// PushSincResampler implementation
PushSincResampler* pushSincResamplerCreate(size_t sourceFrames, size_t destinationFrames) {
    PushSincResampler* pushResampler = (PushSincResampler*)calloc(1, sizeof(PushSincResampler));
    if (!pushResampler) return NULL;

    pushResampler->resampler = sincResamplerCreate(
        (double)sourceFrames / destinationFrames,
        sourceFrames,
        pushSincResamplerCallback,
        pushResampler
    );

    if (!pushResampler->resampler) {
        free(pushResampler);
        return NULL;
    }

    pushResampler->destinationFrames = destinationFrames;
    pushResampler->firstPass = 1;
    pushResampler->sourceAvailable = 0;

    return pushResampler;
}

void pushSincResamplerDestroy(PushSincResampler* resampler) {
    if (!resampler) return;

    if (resampler->floatBuffer) {
        free(resampler->floatBuffer);
    }

    sincResamplerDestroy(resampler->resampler);
    free(resampler);
}

size_t pushSincResamplerResampleInt16(PushSincResampler* resampler,
                                      const int16_t* source,
                                      size_t sourceFrames,
                                      int16_t* destination,
                                      size_t destinationCapacity) {
    if (!resampler || !source || !destination) return 0;
    if (destinationCapacity < resampler->destinationFrames) return 0;

    // Allocate float buffer if needed
    if (!resampler->floatBuffer) {
        resampler->floatBuffer = (float*)malloc(resampler->destinationFrames * sizeof(float));
        if (!resampler->floatBuffer) return 0;
    }

    resampler->sourcePtrInt = source;
    resampler->sourcePtr = NULL;

    // Resample to float buffer
    size_t result = pushSincResamplerResampleFloat(resampler, NULL, sourceFrames,
                                                   resampler->floatBuffer, resampler->destinationFrames);

    // Convert to int16
    floatS16ToS16(resampler->floatBuffer, resampler->destinationFrames, destination);

    resampler->sourcePtrInt = NULL;
    return result;
}

size_t pushSincResamplerResampleFloat(PushSincResampler* resampler,
                                      const float* source,
                                      size_t sourceFrames,
                                      float* destination,
                                      size_t destinationCapacity) {
    if (!resampler || !destination) return 0;
    if (sourceFrames != resampler->resampler->requestFrames) return 0;
    if (destinationCapacity < resampler->destinationFrames) return 0;

    // Cache source pointer
    resampler->sourcePtr = source;
    resampler->sourceAvailable = sourceFrames;

    // On first pass, call resample twice to prime the buffer
    if (resampler->firstPass) {
        sincResamplerResample(resampler->resampler,
                             sincResamplerChunkSize(resampler->resampler),
                             destination);
    }

    sincResamplerResample(resampler->resampler, resampler->destinationFrames, destination);
    resampler->sourcePtr = NULL;

    return resampler->destinationFrames;
}
