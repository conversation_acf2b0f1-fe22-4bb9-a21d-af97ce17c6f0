# SincResampler - C Implementation

A high-quality audio resampler based on the WebRTC SincResampler, converted to pure C with no external dependencies.

## Features

- High-quality sinc-based resampling algorithm
- Support for arbitrary sample rate conversion
- Optimized convolution with Blackman windowing
- Push-based interface for easy integration
- Support for both float and int16 audio data
- No external dependencies (except standard C library)
- Cross-platform compatible

## Files

- `sincResampler.h` - Header file with all function declarations
- `sincResampler.c` - Main implementation
- `testResampler.c` - Test program that processes WAV files
- `example.c` - Simple usage example
- `Makefile` - Build configuration
- `wavhelper/` - WAV file I/O utilities

## Building

### Using Make
```bash
make all          # Build test program
make test         # Build and run tests
make clean        # Clean build files
make install      # Install library and headers
```

### Manual Compilation
```bash
gcc -Wall -O2 -std=c99 -c sincResampler.c
gcc -Wall -O2 -std=c99 -c wavhelper/wavhelper.c
gcc -Wall -O2 -std=c99 -c testResampler.c
gcc -o testResampler sincResampler.o wavhelper.o testResampler.o -lm
```

## Usage

### Basic Example

```c
#include "sincResampler.h"

// Create a push resampler
size_t sourceFrames = 512;      // Input chunk size
size_t destFrames = 1024;       // Output chunk size (for 2x upsampling)
PushSincResampler* resampler = pushSincResamplerCreate(sourceFrames, destFrames);

// Resample float data
float inputBuffer[512];
float outputBuffer[1024];
size_t outputSamples = pushSincResamplerResampleFloat(
    resampler, inputBuffer, sourceFrames, outputBuffer, destFrames);

// Resample int16 data
int16_t inputBuffer16[512];
int16_t outputBuffer16[1024];
size_t outputSamples16 = pushSincResamplerResampleInt16(
    resampler, inputBuffer16, sourceFrames, outputBuffer16, destFrames);

// Cleanup
pushSincResamplerDestroy(resampler);
```

### Advanced Usage with Callback

```c
// Callback function to provide input data
void myCallback(void* userData, size_t frames, float* destination) {
    // Fill destination with input data
    // This is called automatically during resampling
}

// Create low-level resampler
double ratio = 48000.0 / 44100.0;  // 44.1kHz to 48kHz
size_t requestFrames = 512;
SincResampler* resampler = sincResamplerCreate(ratio, requestFrames, myCallback, userData);

// Resample
float outputBuffer[1024];
sincResamplerResample(resampler, 1024, outputBuffer);

// Cleanup
sincResamplerDestroy(resampler);
```

## API Reference

### PushSincResampler (Recommended)

- `PushSincResampler* pushSincResamplerCreate(size_t sourceFrames, size_t destinationFrames)`
- `void pushSincResamplerDestroy(PushSincResampler* resampler)`
- `size_t pushSincResamplerResampleFloat(...)` - Resample float data
- `size_t pushSincResamplerResampleInt16(...)` - Resample int16 data

### SincResampler (Low-level)

- `SincResampler* sincResamplerCreate(double ratio, size_t requestFrames, callback, userData)`
- `void sincResamplerDestroy(SincResampler* resampler)`
- `void sincResamplerResample(SincResampler* resampler, size_t frames, float* destination)`
- `void sincResamplerFlush(SincResampler* resampler)` - Reset internal state
- `void sincResamplerSetRatio(SincResampler* resampler, double ratio)` - Change sample rate ratio

### Utility Functions

- `float sincResamplerAlgorithmicDelaySeconds(int sourceRateHz)` - Calculate processing delay
- `void floatS16ToS16(const float* src, size_t length, int16_t* dest)` - Convert float to int16
- `void s16ToFloatS16(const int16_t* src, size_t length, float* dest)` - Convert int16 to float

## Algorithm Details

The resampler uses a windowed sinc function with the following characteristics:

- **Kernel Size**: 32 samples (adjustable via `KERNEL_SIZE`)
- **Window Function**: Blackman window with α=0.16
- **Interpolation**: Linear interpolation between kernel offsets
- **Offset Count**: 32 sub-sample positions (`KERNEL_OFFSET_COUNT`)
- **Anti-aliasing**: Automatic cutoff frequency adjustment for downsampling

### Quality vs Performance

The current settings provide excellent quality suitable for professional audio applications. To adjust the trade-off:

- Increase `KERNEL_SIZE` for better quality (must be multiple of 32)
- Increase `KERNEL_OFFSET_COUNT` for smoother interpolation
- Decrease both for faster processing with slightly lower quality

## Testing

The test program processes WAV files from the `testwav/` directory and saves resampled versions to `processedwav/`:

```bash
./testResampler
```

Test cases include:
- 16kHz → 48kHz (3x upsampling)
- 22.05kHz → 44.1kHz (2x upsampling)  
- 32kHz → 48kHz (1.5x upsampling)
- 44.1kHz → 16kHz (0.36x downsampling)
- 48kHz → 22.05kHz (0.46x downsampling)

## Performance Notes

- The algorithm has O(n) complexity where n is the number of output samples
- Memory usage is proportional to the kernel size and request frame size
- SIMD optimizations from the original WebRTC code have been removed for portability
- For real-time applications, choose request frame sizes that match your audio buffer sizes

## Limitations

- Single-channel processing only (process each channel separately for multi-channel audio)
- Fixed-point arithmetic not supported (float only internally)
- No built-in dithering for int16 output
- Memory allocations are not real-time safe

## License

Based on the WebRTC project (BSD-style license). This C port maintains the same licensing terms.

## Differences from Original WebRTC Code

1. **Language**: Converted from C++ to C
2. **Dependencies**: Removed all WebRTC-specific dependencies
3. **Memory Management**: Simplified aligned memory allocation
4. **SIMD**: Removed architecture-specific optimizations for portability
5. **Naming**: Changed to camelCase convention
6. **Interface**: Added push-based wrapper for easier usage
7. **Error Handling**: Added more robust error checking
